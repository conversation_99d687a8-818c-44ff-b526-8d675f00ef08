<?php
defined('BASEPATH') or die('No direct script access allowed!');

function JSONResponse($data = array())
{
    $CI = &get_instance();
    $CI->output->set_content_type('application/json');

    echo json_encode($data);
}

function JSONResponseDefault($result, $message)
{
    return JSONResponse(array(
        'RESULT' => $result,
        'MESSAGE' => $message
    ));
}

function getWorkunitData($workunitid)
{
    if (empty($workunitid)) {
        return 'N/A';
    }

    $CI = &get_instance();
    $workunit = $CI->db->select('workunitname')
        ->from('msworkunits')
        ->where('id', $workunitid)
        ->get()
        ->row();

    if ($workunit) {
        return $workunit->workunitname;
    }

    return 'N/A';
}

function getPost($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->post($index)) {
        if (!is_array($CI->input->post($index))) {
            return trim($CI->input->post($index));
        } else {
            return $CI->input->post($index);
        }
    }

    return $default;
}

function getGet($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->get($index)) {
        return $CI->input->get($index);
    }

    return $default;
}

function getModels($models = array())
{
    $ci = &get_instance();

    foreach ($models as $key => $value) {
        $ci->load->model($key, $value);
    }
}

function getSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->userdata($index);
}

function setSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->set_userdata($index);
}

function hasSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->has_userdata($index);
}

function unsetSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->unset_userdata($index);
}

function destroySession()
{
    $CI = &get_instance();

    return $CI->session->sess_destroy();
}

function getCurrentIdUser()
{
    return getSessionValue('ID');
}

function getCurrentUser($id = null)
{
    $CI = &get_instance();

    return $CI->db->get_where('msusers', array('id' => $id ?? getCurrentIdUser()))->row();
}

function isLogin()
{
    return getSessionValue('ID');
}

function isSuperAdmin()
{
    return getSessionValue('ROLE') == 'Super Admin';
}

function isCustomerService()
{
    return getSessionValue('ROLE') == 'Customer Service';
}

function isBumdesUser()
{
    return getSessionValue('ROLE') == 'BUMDes User';
}

function isBumdes()
{
    return getSessionValue('ROLE') == 'BUMDes';
}

function getBumdesId()
{
    return getSessionValue('BUMDES_ID');
}

function getWorkunitId()
{
    return getSessionValue('WORKUNIT_ID');
}

function getBumdesName()
{
    return getSessionValue('BUMDES_NAME');
}

function getWorkunitName()
{
    return getSessionValue('WORKUNIT_NAME');
}

function getVillageId()
{
    // For BUMDes users, get village from their BUMDes
    if (isBumdesUser()) {
        $CI = &get_instance();
        $bumdes = $CI->db->get_where('msusers', array('id' => getBumdesId()))->row();
        return $bumdes ? $bumdes->villageid : null;
    }

    // For BUMDes access, get village from current user
    if (isBumdes()) {
        $current_user = getCurrentUser();
        return $current_user ? $current_user->villageid : null;
    }

    return null;
}

function getVillageName()
{
    $village_id = getVillageId();
    if (!$village_id) {
        return null;
    }

    $CI = &get_instance();
    $village = $CI->db->get_where('msvillages', array('code' => $village_id))->row();
    return $village ? $village->name : null;
}

function isFromSameVillage($user_id)
{
    $current_village_id = getVillageId();
    if (!$current_village_id) {
        return false;
    }

    $CI = &get_instance();
    $user = $CI->db->get_where('msusers', array('id' => $user_id))->row();

    return $user && $user->villageid == $current_village_id;
}

if (!function_exists('str_contains')) {
    function str_contains($needle, $haystack)
    {
        return stripos($needle, $haystack) !== false;
    }
}

function IDR($nominal, $digit = 0, $pemisah = '.', $rupiah = ',')
{
    return number_format($nominal, $digit, $pemisah, $rupiah);
}

function getCurrentDate($format = 'Y-m-d H:i:s')
{
    date_default_timezone_set("Asia/Jakarta");
    return date($format);
}

function getSelectedYear()
{
    $CI = &get_instance();

    // Check if year filter is being set via POST
    if ($CI->input->post('set_year_filter') && $CI->input->post('selected_year')) {
        $year = $CI->input->post('selected_year');
        $CI->session->set_userdata('selected_year', $year);
        return $year;
    }

    // Get from session or default to current year
    $selectedYear = $CI->session->userdata('selected_year');
    return $selectedYear ? $selectedYear : date('Y');
}

function setSelectedYear($year)
{
    $CI = &get_instance();
    $CI->session->set_userdata('selected_year', $year);
}

function getContents($feature, $name, $row = null, $other = array())
{
    $output = array();
    $ci = &get_instance();

    switch ($feature) {


        case 'beginningbalance':
            if ($name == 'index') {
                $output = array(
                    'title' => 'Daftar Saldo Awal',
                    'table' => [
                        'workunitdata' => 'Unit Kerja',
                        'period' => 'Periode',
                        'beginning_balances' => 'Saldo Awal',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'button',
                                    'onclick' => 'showAddBalanceModal(${table.primary})',
                                    'class' => 'btn btn-success btn-sm me-1',
                                    'icon' => 'ri-add-line',
                                    'text' => 'Tambah'
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'showReduceBalanceModal(${table.primary})',
                                    'class' => 'btn btn-warning btn-sm me-1',
                                    'icon' => 'ri-subtract-line',
                                    'text' => 'Kurang'
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'showBalanceHistory(${table.primary})',
                                    'class' => 'btn btn-info btn-sm',
                                    'icon' => 'ri-history-line',
                                    'text' => 'Riwayat'
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'select' => 'a.*, b.workunitname as workunitdata',
                        'join' => [
                            'msworkunits b' => 'a.workunitid = b.id'
                        ],
                        'where' => [
                            // BUMDes users can see beginning balance from their BUMDes
                            // BUMDes access can see beginning balance from their own data
                            'a.createdby' => isBumdesUser() ? getBumdesId() : getCurrentIdUser()
                        ],
                        'method' => 'result'
                    ]
                );
            } else if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'title' => $name == 'add' ? 'Tambah Saldo Awal' : 'Ubah Saldo Awal',
                    'fields' => [
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Periode Saldo Awal (Tahun)',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'required_period',
                                    'placeholder' => 'Masukkan Tahun (contoh: 2024)',
                                    'value' => $name == 'edit' && isset($row->period) ? date('Y', strtotime($row->period)) : date('Y'),
                                    'required' => true,
                                    'attr' => [
                                        'min' => '2020',
                                        'max' => '2050'
                                    ]
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Unit Usaha',
                                    'type' => 'select',
                                    'variable' => 'required_workunitid',
                                    'defaultvalue' => [
                                        'data' => isBumdesUser() ?
                                            // BUMDes users can only select their own work unit
                                            $ci->db->query("
                                                SELECT w.* FROM msworkunits w
                                                WHERE w.id = " . getWorkunitId() . "
                                                ORDER BY w.workunitname
                                            ")->result() :
                                            // BUMDes access can select from their available work units
                                            $ci->db->query("
                                                SELECT w.* FROM msworkunits w
                                                WHERE w.id IN (
                                                    SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(u.workunitid, ',', numbers.n), ',', -1)) as workunit_id
                                                    FROM msusers u
                                                    CROSS JOIN (
                                                        SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
                                                    ) numbers
                                                    WHERE u.id = " . getCurrentIdUser() . "
                                                    AND CHAR_LENGTH(u.workunitid) - CHAR_LENGTH(REPLACE(u.workunitid, ',', '')) >= numbers.n - 1
                                                )
                                                ORDER BY w.workunitname
                                            ")->result(),
                                        'value' => 'id',
                                        'text' => 'workunitname',
                                        'selected' => $name == 'edit' ? $row->workunitid : (isBumdesUser() ? getWorkunitId() : null)
                                    ],
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Saldo Awal',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'required_beginning_balances',
                                    'placeholder' => 'Masukkan Saldo Awal',
                                    'required' => true,
                                ],
                            ]
                        ]
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger btn-sm',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary btn-sm',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'transaction':
            if ($name == 'index') {
                $output = array(
                    'title' => 'Daftar Transaksi',
                    'table' => [
                        'transactioncode' => 'Kode Transaksi',
                        'transactiondate' => 'Tanggal Transaksi',
                        'transactionnote' => 'Catatan Transaksi',
                        'transactiontype' => 'Tipe Transaksi',
                        'workunitdata' => 'Unit Kerja',
                        'status' => 'Status',
                        'amount' => 'Nominal',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/transaction/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'ri-edit-line',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.judul_dokumen}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'ri-delete-bin-line',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'select' => 'a.*, COALESCE(b.workunitname, "N/A") as workunitdata',
                        'join' => [
                            'msworkunits b' => 'a.workunitid = b.id'
                        ],
                        'where' => isBumdesUser() ? [
                            // BUMDes users: filter by their work unit and BUMDes
                            'a.workunitid' => getWorkunitId(),
                            'a.createdby' => getBumdesId()
                        ] : [
                            // BUMDes access and others: filter by current user
                            // Note: BUMDes village filtering will be handled in a custom controller
                            'a.createdby' => getCurrentIdUser()
                        ],
                        'method' => 'result'
                    ]
                );
            }
            break;

        case 'customer':
            if ($name == 'index') {
                $output = array(
                    'title' => 'Daftar Pelanggan',
                    'table' => [
                        'name' => 'Nama Pelanggan',
                        'dateofbirth' => 'Tanggal Lahir',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/customer/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'ri-edit-line',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.judul_dokumen}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'ri-delete-bin-line',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'where' => [
                            'createdby' => getCurrentIdUser()
                        ],
                        'method' => 'result'
                    ]
                );
            } else if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'title' => $name == 'add' ? 'Tambah Pelanggan' : 'Ubah Pelanggan',
                    'fields' => [
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nama Pelanggan',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_name',
                                    'placeholder' => 'Masukkan Nama Pelanggan',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nomor WhatsApp',
                                    'type' => 'input',
                                    'input_type' => 'number',
                                    'variable' => 'required_phonenumber',
                                    'placeholder' => 'Masukkan Nomor WhatsApp',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Tanggal Lahir',
                                    'type' => 'input',
                                    'input_type' => 'date',
                                    'variable' => 'required_dateofbirth',
                                    'placeholder' => 'Masukkan Tanggal Lahir',
                                    'required' => true,
                                ]
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger btn-sm',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary btn-sm',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'superadmin':
            if ($name == 'index') {
                $output = array(
                    'title' => 'Daftar Super Admin',
                    'table' => [
                        'name' => 'Nama',
                        'username' => 'Username',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/superadmin/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'ri-edit-line',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.judul_dokumen}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'ri-delete-bin-line',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'where' => [
                            'role' => 'Super Admin'
                        ],
                        'method' => 'result'
                    ]
                );
            } else if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'title' => $name == 'add' ? 'Tambah Super Admin' : 'Ubah Super Admin',
                    'fields' => [
                        [
                            'type' => 'input',
                            'input_type' => 'hidden',
                            'variable' => 'required_role',
                            'default' => 'Super Admin'
                        ],
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Username',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_validated_username',
                                    'placeholder' => 'Masukkan Username',
                                    'required' => true,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Password',
                                    'type' => 'input',
                                    'input_type' => 'password',
                                    'variable' => 'password',
                                    'placeholder' => 'Masukkan Password',
                                    'required' => $name == 'add' ? true : false,
                                ],
                                [
                                    'parent_class' => 'col-md-4',
                                    'label' => 'Nama',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_name',
                                    'placeholder' => 'Masukkan Nama',
                                    'required' => true,
                                ]
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger btn-sm',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary btn-sm',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        case 'workunit':
            if ($name == 'index') {
                $output = array(
                    'title' => 'Daftar Unit Usaha',
                    'table' => [
                        'workunitname' => 'Nama Unit Usaha',
                        'action' => [
                            'title' => 'Aksi',
                            'items' => [
                                [
                                    'type' => 'a',
                                    'href' => base_url('master/workunit/edit/${table.primary}'),
                                    'class' => 'btn btn-primary btn-sm',
                                    'icon' => 'ri-edit-line',
                                ],
                                [
                                    'type' => 'button',
                                    'onclick' => 'deleteData(\'${table.value.judul_dokumen}\', ${table.primary})',
                                    'class' => 'btn btn-danger btn-sm',
                                    'icon' => 'ri-delete-bin-line',
                                ]
                            ]
                        ]
                    ],
                    'database' => [
                        'method' => 'result'
                    ]
                );
            } else if ($name == 'add' || $name == 'edit') {
                $output = array(
                    'row_data' => $row,
                    'title' => $name == 'add' ? 'Tambah Unit Usaha' : 'Ubah Unit Usaha',
                    'fields' => [
                        [
                            'type' => 'row',
                            'data' => [
                                [
                                    'parent_class' => 'col-md-12',
                                    'label' => 'Nama Unit Usaha',
                                    'type' => 'input',
                                    'input_type' => 'text',
                                    'variable' => 'required_workunitname',
                                    'placeholder' => 'Masukkan Nama Unit Usaha',
                                    'required' => true,
                                ]
                            ]
                        ],
                    ],
                    'footer_button' => [
                        [
                            'type' => 'a',
                            'class' => 'btn btn-danger btn-sm',
                            'text' => 'Kembali',
                            'href' => base_url('master/' . $feature),
                        ],
                        [
                            'type' => 'button',
                            'class' => 'btn btn-primary btn-sm',
                            'text' => 'Simpan'
                        ]
                    ]
                );
            }
            break;

        default:
            break;
    }

    return $output;
}

function getFieldParameter($feature, $name)
{
    $variable = array();
    foreach (getContents($feature, $name)['fields'] as $key => $value) {
        if (isset($value['variable'])) {
            if (isset($value['required'])) {
                $variable[] = $value['variable'];
            }
        } else if (isset($value['data'])) {
            foreach ($value['data'] as $k => $v) {
                if (isset($v['required'])) {
                    $variable[] = $v['variable'];
                }
            }
        }
    }

    return $variable;
}

function stringEncryption($action, $string)
{
    $output = false;

    $encrypt_method = 'AES-256-CBC'; // Default
    $secret_key = 'karpeldedvtech'; // Change the key!
    $secret_iv = 'owr216he890';  // Change the init vector!

    // hash
    $key = hash('sha256', $secret_key);

    // iv - encrypt method AES-256-CBC expects 16 bytes - else you will get a warning
    $iv = substr(hash('sha256', $secret_iv), 0, 16);

    if ($action == 'encrypt') {
        $output = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);
    } else if ($action == 'decrypt') {
        $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
    }

    return $output;
}

function validateDate($dateStr, $format)
{
    date_default_timezone_set('UTC');
    if ($dateStr != null) {
        $date = DateTime::createFromFormat($format, $dateStr);
        return $date && ($date->format($format) === $dateStr);
    } else {
        return false;
    }
}

function tgl_indo($tanggal)
{
    $bulan = array(
        'Januari',
        'Februari',
        'Maret',
        'April',
        'Mei',
        'Juni',
        'Juli',
        'Agustus',
        'September',
        'Oktober',
        'November',
        'Desember'
    );

    $pecahkan = explode('-', $tanggal);

    if (isset($pecahkan[0]) && isset($pecahkan[1]) && isset($pecahkan[2])) {
        return $pecahkan[2] . ' ' . $bulan[(int)$pecahkan[1] - 1] . ' ' . $pecahkan[0];
    } else {
        return date('d F Y', strtotime($tanggal));
    }
}

function getCurrentProfile()
{
    return base_url('assets/img/avatar/avatar-1.png');
}

function generateRandomString($length = 10)
{
    // create variable $characters with value '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    // create variable $charactersLength with value strlen($characters)
    $charactersLength = strlen($characters);
    // create variable $randomString with value ''
    $randomString = '';
    // create loop for generate random string
    for ($i = 0; $i < $length; $i++) {
        // create variable $randomString with value $characters[rand(0, $charactersLength - 1)]
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    // return $randomString
    return $randomString;
}

// create function to get day name indonesia    
function getDayIndo($date)
{
    $day = date('D', strtotime($date));

    switch ($day) {
        case 'Sun':
            $hari = "Minggu";
            break;

        case 'Mon':
            $hari = "Senin";
            break;

        case 'Tue':
            $hari = "Selasa";
            break;

        case 'Wed':
            $hari = "Rabu";
            break;

        case 'Thu':
            $hari = "Kamis";
            break;

        case 'Fri':
            $hari = "Jumat";
            break;

        case 'Sat':
            $hari = "Sabtu";
            break;

        default:
            $hari = "Tidak di ketahui";
            break;
    }

    return $hari;
}
