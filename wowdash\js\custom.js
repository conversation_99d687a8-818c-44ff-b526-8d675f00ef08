/**
 * Custom JavaScript functions for BUMDes System
 */

// Currency formatting functions
function formatCurrency(amount) {
    if (amount === null || amount === undefined || amount === '') {
        return '0';
    }
    
    // Remove any non-numeric characters except decimal point
    let numericValue = amount.toString().replace(/[^\d.-]/g, '');
    
    // Convert to number and format with thousand separators
    let number = parseFloat(numericValue) || 0;
    return number.toLocaleString('id-ID');
}

function formatCurrencyInput(input) {
    let value = input.value.replace(/[^\d]/g, '');
    if (value) {
        input.value = formatCurrency(value);
    }
}

function unformatCurrency(formattedValue) {
    if (!formattedValue) return 0;
    return parseFloat(formattedValue.toString().replace(/[^\d.-]/g, '')) || 0;
}

// Initialize currency formatting for input fields
function initializeCurrencyFormatting() {
    // Apply to all inputs with currency class
    $('.currency-input').each(function() {
        const input = this;
        
        // Format on input
        $(input).on('input', function() {
            formatCurrencyInput(this);
        });
        
        // Format on blur
        $(input).on('blur', function() {
            formatCurrencyInput(this);
        });
        
        // Format initial value if exists
        if (input.value) {
            formatCurrencyInput(input);
        }
    });
    
    // Apply to specific input names/ids commonly used for money
    const currencySelectors = [
        'input[name="amount"]',
        'input[name="balance"]',
        'input[name="hpp_amount"]',
        'input[name="modal_amount"]',
        'input[name="omset_amount"]',
        'input[name="laba_amount"]',
        'input[id*="amount"]',
        'input[id*="balance"]',
        'input[id*="modal"]',
        'input[id*="omset"]',
        'input[id*="laba"]'
    ];
    
    currencySelectors.forEach(function(selector) {
        $(selector).each(function() {
            const input = this;
            
            // Add currency-input class if not already present
            if (!$(input).hasClass('currency-input')) {
                $(input).addClass('currency-input');
                
                // Format on input
                $(input).on('input', function() {
                    formatCurrencyInput(this);
                });
                
                // Format on blur
                $(input).on('blur', function() {
                    formatCurrencyInput(this);
                });
                
                // Format initial value if exists
                if (input.value) {
                    formatCurrencyInput(input);
                }
            }
        });
    });
}

// Form submission handler to unformat currency values
function handleCurrencyFormSubmission() {
    $('form').on('submit', function() {
        $(this).find('.currency-input, input[name="amount"], input[name="balance"], input[name="hpp_amount"], input[name="modal_amount"], input[name="omset_amount"], input[name="laba_amount"]').each(function() {
            const originalValue = this.value;
            const unformattedValue = unformatCurrency(originalValue);
            
            // Create a hidden input with the unformatted value
            const hiddenInput = $('<input type="hidden">');
            hiddenInput.attr('name', this.name);
            hiddenInput.val(unformattedValue);
            
            // Change the original input name to avoid conflicts
            this.name = this.name + '_formatted';
            
            // Add hidden input to form
            $(this).after(hiddenInput);
        });
    });
}

// Year filter functionality
function initializeYearFilter() {
    // Get current year from session or default to current year
    let currentYear = sessionStorage.getItem('selected_year') || new Date().getFullYear();
    
    // Set the year filter dropdown value
    if ($('#year-filter').length) {
        $('#year-filter').val(currentYear);
    }
    
    // Handle year filter change
    $('#year-filter').on('change', function() {
        const selectedYear = $(this).val();
        sessionStorage.setItem('selected_year', selectedYear);
        
        // Reload page to apply filter
        window.location.reload();
    });
}

// Initialize all custom functions when document is ready
$(document).ready(function() {
    initializeCurrencyFormatting();
    handleCurrencyFormSubmission();
    initializeYearFilter();
});

// Re-initialize currency formatting for dynamically added content
function reinitializeCurrencyFormatting() {
    initializeCurrencyFormatting();
}
