<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="auth-wrapper d-flex align-items-center justify-content-center min-vh-100">
    <div class="auth-content">
        <div class="card">
            <div class="card-body">
                <div class="text-center mb-4">
                    <h4 class="mb-2">Registrasi BUMDes</h4>
                    <p class="text-muted">Daftarkan BUMDes Anda untuk mengakses sistem</p>
                </div>

                <form id="frmRegister" action="<?= base_url('register/process') ?>" method="POST" autocomplete="off">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="business_owner" class="form-label fw-semibold text-primary-light text-sm mb-8">Nama Pemilik Usaha <span class="text-danger">*</span></label>
                                <input type="text" class="form-control radius-8" id="business_owner" name="business_owner" placeholder="Masukkan nama pemilik usaha" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="business_name" class="form-label fw-semibold text-primary-light text-sm mb-8">Nama Usaha <span class="text-danger">*</span></label>
                                <input type="text" class="form-control radius-8" id="business_name" name="business_name" placeholder="Masukkan nama usaha" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label fw-semibold text-primary-light text-sm mb-8">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control radius-8" id="username" name="username" placeholder="Masukkan username" required>
                                <small class="text-muted">Minimal 4 karakter</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label fw-semibold text-primary-light text-sm mb-8">Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control radius-8" id="password" name="password" placeholder="Masukkan password" required>
                                <small class="text-muted">Minimal 6 karakter</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label fw-semibold text-primary-light text-sm mb-8">Konfirmasi Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control radius-8" id="confirm_password" name="confirm_password" placeholder="Konfirmasi password" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="city_id" class="form-label fw-semibold text-primary-light text-sm mb-8">Kabupaten/Kota <span class="text-danger">*</span></label>
                                <select class="form-control radius-8" id="city_id" name="city_id" required>
                                    <option value="">Pilih Kabupaten/Kota</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="district_id" class="form-label fw-semibold text-primary-light text-sm mb-8">Kecamatan <span class="text-danger">*</span></label>
                                <select class="form-control radius-8" id="district_id" name="district_id" required>
                                    <option value="">Pilih Kecamatan</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="village_id" class="form-label fw-semibold text-primary-light text-sm mb-8">Desa <span class="text-danger">*</span></label>
                                <select class="form-control radius-8" id="village_id" name="village_id" required>
                                    <option value="">Pilih Desa</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="workunit_ids" class="form-label fw-semibold text-primary-light text-sm mb-8">Unit Usaha <span class="text-danger">*</span></label>
                        <select class="form-control radius-8" id="workunit_ids" name="workunit_ids[]" multiple required>
                            <?php foreach ($workunits as $workunit): ?>
                                <option value="<?= $workunit->id ?>"><?= $workunit->workunitname ?></option>
                            <?php endforeach; ?>
                        </select>
                        <small class="text-muted">Pilih unit usaha yang akan dikelola (dapat memilih lebih dari satu)</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="agree_terms" required>
                            <label class="form-check-label" for="agree_terms">
                                Saya setuju dengan syarat dan ketentuan yang berlaku
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 radius-8 py-12">
                        Daftar Sekarang
                    </button>
                </form>

                <div class="text-center mt-4">
                    <p class="mb-0">Sudah punya akun? <a href="<?= base_url('auth/login') ?>" class="text-primary">Login di sini</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        // Initialize Select2 for work units
        $('#workunit_ids').select2({
            placeholder: 'Pilih unit usaha',
            allowClear: true
        });

        // Load cities on page load
        loadCities();

        // City change event
        $('#city_id').change(function() {
            const cityCode = $(this).val();
            $('#district_id').html('<option value="">Pilih Kecamatan</option>');
            $('#village_id').html('<option value="">Pilih Desa</option>');
            
            if (cityCode) {
                loadDistricts(cityCode);
            }
        });

        // District change event
        $('#district_id').change(function() {
            const districtCode = $(this).val();
            $('#village_id').html('<option value="">Pilih Desa</option>');
            
            if (districtCode) {
                loadVillages(districtCode);
            }
        });

        // Form submission
        $.AjaxRequest('#frmRegister', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.href = '<?= base_url('auth/login') ?>';
                    })
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    };

    function loadCities() {
        $.post('<?= base_url('register/get_cities') ?>', {}, function(response) {
            let options = '<option value="">Pilih Kabupaten/Kota</option>';
            response.forEach(function(city) {
                options += `<option value="${city.code}">${city.name}</option>`;
            });
            $('#city_id').html(options);
        });
    }

    function loadDistricts(cityCode) {
        $.post('<?= base_url('register/get_districts') ?>', {city_code: cityCode}, function(response) {
            let options = '<option value="">Pilih Kecamatan</option>';
            response.forEach(function(district) {
                options += `<option value="${district.code}">${district.name}</option>`;
            });
            $('#district_id').html(options);
        });
    }

    function loadVillages(districtCode) {
        $.post('<?= base_url('register/get_villages') ?>', {district_code: districtCode}, function(response) {
            let options = '<option value="">Pilih Desa</option>';
            response.forEach(function(village) {
                options += `<option value="${village.code}">${village.name}</option>`;
            });
            $('#village_id').html(options);
        });
    }
</script>
