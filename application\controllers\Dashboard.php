<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property CI_DB_mysqli_driver $db
 * @property Transactions $transactions
 * @property Customers $customers
 * @property Tb_Broadcast $tb_broadcast (temporarily disabled)

 */
class Dashboard extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Transactions', 'transactions');
        $this->load->model('Customers', 'customers');
        // $this->load->model('Tb_Broadcast', 'tb_broadcast'); // Temporarily disabled

        $this->load->model('Bumdes_reports', 'bumdes_reports');
        $this->load->model('Beginningbalances', 'beginningbalances');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Dashboard';

        // Get selected year for filtering
        $selectedYear = getSelectedYear();

        if (isSuperAdmin()) {
            // Dashboard untuk Super Admin - Monitoring Keuangan Desa
            $data['content'] = 'dashboard_superadmin';

            // Load model untuk data desa/BUMDes
            $this->load->model('Superadmins', 'superadmins');
            $this->load->model('Bumdes_reports', 'bumdes_reports');

            // Total pendapatan semua desa tahun ini
            $data['total_income_all'] = $this->transactions->sum('amount', [
                'transactiontype' => 'Pendapatan',
                'status' => 'Lunas',
                'YEAR(transactiondate)' => $selectedYear,
            ]) ?? 0;

            // Total pengeluaran semua desa tahun ini
            $data['total_expense_all'] = $this->transactions->sum('amount', [
                'transactiontype' => 'Pengeluaran',
                'status' => 'Lunas',
                'YEAR(transactiondate)' => $selectedYear,
            ]) ?? 0;

            // Total BUMDes yang terdaftar
            $data['total_bumdes'] = $this->superadmins->total(array(
                'role' => 'BUMDes',
            ));

            // Total modal BUMDes keseluruhan dari laporan yang sudah disubmit
            $bumdes_stats = $this->bumdes_reports->getSuperAdminStatistics();
            $data['total_modal_bumdes'] = $bumdes_stats['total_modal_all'] ?? 0;

            // Total transaksi semua desa
            $data['total_transactions_all'] = $this->transactions->total();

            // Data keuangan per BUMDes/Desa - menggunakan pendekatan yang lebih aman
            // Cek apakah tabel transactions ada
            if ($this->db->table_exists('transactions')) {
                $data['financial_summary'] = $this->db->query("
                    SELECT
                        u.name as bumdes_name,
                        u.role,
                        v.name as village_name,
                        d.name as district_name,
                        c.name as city_name,
                        COALESCE(income.total, 0) as total_income,
                        COALESCE(expense.total, 0) as total_expense,
                        COALESCE(income.total, 0) - COALESCE(expense.total, 0) as net_income,
                        COALESCE(trans_count.total, 0) as transaction_count
                    FROM msusers u
                    LEFT JOIN msvillages v ON u.villageid = CAST(v.code AS UNSIGNED)
                    LEFT JOIN msdistricts d ON v.district_code = d.code
                    LEFT JOIN mscities c ON d.city_code = c.code
                    LEFT JOIN (
                        SELECT createdby, SUM(amount) as total
                        FROM transactions
                        WHERE transactiontype = 'Pendapatan'
                        AND status = 'Lunas'
                        AND YEAR(transactiondate) = " . date('Y') . "
                        GROUP BY createdby
                    ) income ON u.id = income.createdby
                    LEFT JOIN (
                        SELECT createdby, SUM(amount) as total
                        FROM transactions
                        WHERE transactiontype = 'Pengeluaran'
                        AND status = 'Lunas'
                        AND YEAR(transactiondate) = " . date('Y') . "
                        GROUP BY createdby
                    ) expense ON u.id = expense.createdby
                    LEFT JOIN (
                        SELECT createdby, COUNT(*) as total
                        FROM transactions
                        WHERE status = 'Lunas'
                        AND YEAR(transactiondate) = " . date('Y') . "
                        GROUP BY createdby
                    ) trans_count ON u.id = trans_count.createdby
                    WHERE u.role IN ('BUMDes', 'BPD')
                    ORDER BY net_income DESC
                ")->result();
            } else {
                // Jika tabel transactions belum ada, ambil data BUMDes dengan informasi wilayah
                $data['financial_summary'] = $this->db->select('u.name as bumdes_name, u.role, v.name as village_name, d.name as district_name, c.name as city_name')
                    ->from('msusers u')
                    ->join('msvillages v', 'u.villageid = CAST(v.code AS UNSIGNED)', 'left')
                    ->join('msdistricts d', 'v.district_code = d.code', 'left')
                    ->join('mscities c', 'd.city_code = c.code', 'left')
                    ->where('u.role', 'BUMDes')
                    ->get()
                    ->result();

                // Set default values untuk financial data
                foreach ($data['financial_summary'] as $summary) {
                    $summary->total_income = 0;
                    $summary->total_expense = 0;
                    $summary->net_income = 0;
                    $summary->transaction_count = 0;
                }
            }

            // Data untuk grafik bulanan
            $months = array();
            for ($i = 1; $i <= 12; $i++) {
                $month_name = date('M', mktime(0, 0, 0, $i, 1));
                $income = $this->transactions->sum('amount', [
                    'transactiontype' => 'Pendapatan',
                    'status' => 'Lunas',
                    'YEAR(transactiondate)' => date('Y'),
                    'MONTH(transactiondate)' => $i,
                ]) ?? 0;
                $expense = $this->transactions->sum('amount', [
                    'transactiontype' => 'Pengeluaran',
                    'status' => 'Lunas',
                    'YEAR(transactiondate)' => date('Y'),
                    'MONTH(transactiondate)' => $i,
                ]) ?? 0;

                $months[$month_name] = array(
                    'revenue' => $income,
                    'expense' => $expense
                );
            }

            $data['months'] = json_encode(array_keys($months));
            $data['chart_revenue'] = json_encode(array_column($months, 'revenue'));
            $data['chart_expense'] = json_encode(array_column($months, 'expense'));

            // Transaksi terbaru dari semua desa (tanpa financial accounts)
            if ($this->db->table_exists('transactions')) {
                // Ambil transaksi terbaru dengan informasi BUMDes (work unit akan dihandle oleh helper)
                $data['recent_transactions'] = $this->db->select('a.*, u.name as bumdes_name, u.role as user_role, v.name as village_name')
                    ->from('transactions a')
                    ->join('msusers u', 'u.id = a.createdby', 'left')
                    ->join('msvillages v', 'u.villageid = CAST(v.code AS UNSIGNED)', 'left')
                    ->order_by('a.transactiondate DESC, a.createddate DESC')
                    ->limit(10)
                    ->get()
                    ->result();
            } else {
                $data['recent_transactions'] = array();
            }
        } elseif (isBumdesUser()) {
            // Dashboard untuk Pengguna BUMDes
            $data['content'] = 'dashboard_bumdes_user';

            // Filter transaksi berdasarkan unit kerja pengguna dan BUMDes yang sama
            // Pengguna BUMDes hanya melihat data dari unit kerja mereka di desa yang sama
            $workunit_filter_with_alias = array(
                'a.workunitid' => getWorkunitId(),
                'a.createdby' => getBumdesId(), // Transaksi harus dari BUMDes yang sama
            );

            // Filter untuk queries yang tidak menggunakan table alias (seperti sum method)
            $workunit_filter = array(
                'workunitid' => getWorkunitId(),
                'createdby' => getBumdesId(), // Transaksi harus dari BUMDes yang sama
            );

            $data['transactions'] = $this->transactions->select('a.*')
                ->order_by('a.transactiondate, a.createddate', 'DESC')
                ->limit(10)
                ->result($workunit_filter_with_alias);

            $data['total_income'] = $this->transactions->sum('amount', array_merge($workunit_filter, [
                'transactiontype' => 'Pendapatan',
                'status' => 'Lunas',
                'YEAR(transactiondate)' => date('Y'),
            ])) ?? 0;

            $data['total_expense'] = $this->transactions->sum('amount', array_merge($workunit_filter, [
                'transactiontype' => 'Pengeluaran',
                'status' => 'Lunas',
                'YEAR(transactiondate)' => date('Y'),
            ])) ?? 0;

            $data['total_transactions'] = $this->transactions->total(array_merge($workunit_filter_with_alias, [
                'YEAR(a.transactiondate)' => date('Y'),
            ]));

            // Data untuk grafik bulanan
            $months = array();
            for ($i = 1; $i <= 12; $i++) {
                $month_name = date('F', mktime(0, 0, 0, $i, 10));

                $months[$month_name]['revenue'] = $this->transactions->sum('amount', array_merge($workunit_filter, [
                    'transactiontype' => 'Pendapatan',
                    'status' => 'Lunas',
                    'MONTH(transactiondate)' => $i,
                    'YEAR(transactiondate)' => date('Y'),
                ])) ?? 0;
                $months[$month_name]['expense'] = $this->transactions->sum('amount', array_merge($workunit_filter, [
                    'transactiontype' => 'Pengeluaran',
                    'status' => 'Lunas',
                    'MONTH(transactiondate)' => $i,
                    'YEAR(transactiondate)' => date('Y'),
                ])) ?? 0;
            }

            $data['months'] = json_encode(array_keys($months));
            $data['chart_revenue'] = json_encode(array_column($months, 'revenue'));
            $data['chart_expense'] = json_encode(array_column($months, 'expense'));

            // Info unit kerja
            $data['workunit_name'] = getWorkunitName();
            $data['bumdes_name'] = getBumdesName();
        } else {
            // Dashboard untuk BUMDes biasa - hanya data dari desa mereka sendiri
            $data['content'] = 'dashboard';

            // Filter transaksi: data dari BUMDes sendiri + data dari pengguna BUMDes di desa yang sama
            $village_id = getVillageId();
            $current_user_id = getCurrentIdUser();

            // Get all BUMDes users from the same village
            $village_bumdes_users = array();
            if ($village_id) {
                $this->load->model('Bumdes_user_model', 'bumdes_users');
                $bumdes_users_in_village = $this->bumdes_users->select('a.id')
                    ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                    ->result(array('b.villageid' => $village_id));

                foreach ($bumdes_users_in_village as $user) {
                    $village_bumdes_users[] = $user->id;
                }
            }

            // Include current BUMDes and all BUMDes users from same village
            $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

            if (!empty($allowed_creators)) {
                $data['transactions'] = $this->transactions->select('a.*')
                    ->order_by('a.transactiondate, a.createddate', 'DESC')
                    ->limit(10)
                    ->where_in('a.createdby', $allowed_creators)
                    ->result();

                // Calculate totals using custom query for multiple creators
                $income_query = $this->db->select('SUM(amount) as total')
                    ->from('transactions')
                    ->where('transactiontype', 'Pendapatan')
                    ->where('status', 'Lunas')
                    ->where('YEAR(transactiondate)', date('Y'))
                    ->where_in('createdby', $allowed_creators)
                    ->get();
                $data['total_income'] = $income_query->row()->total ?? 0;

                $expense_query = $this->db->select('SUM(amount) as total')
                    ->from('transactions')
                    ->where('transactiontype', 'Pengeluaran')
                    ->where('status', 'Lunas')
                    ->where('YEAR(transactiondate)', date('Y'))
                    ->where_in('createdby', $allowed_creators)
                    ->get();
                $data['total_expense'] = $expense_query->row()->total ?? 0;

                $data['total_transactions'] = $this->transactions->where_in('createdby', $allowed_creators)->total();
            } else {
                // Fallback to only current user data
                $data['transactions'] = $this->transactions->select('a.*')
                    ->order_by('a.transactiondate, a.createddate', 'DESC')
                    ->limit(10)
                    ->result(array(
                        'a.createdby' => $current_user_id,
                    ));
                $data['total_income'] = $this->transactions->sum('amount', [
                    'transactiontype' => 'Pendapatan',
                    'status' => 'Lunas',
                    'YEAR(transactiondate)' => date('Y'),
                    'createdby' => $current_user_id,
                ]) ?? 0;
                $data['total_expense'] = $this->transactions->sum('amount', [
                    'transactiontype' => 'Pengeluaran',
                    'status' => 'Lunas',
                    'YEAR(transactiondate)' => date('Y'),
                    'createdby' => $current_user_id,
                ]) ?? 0;
                $data['total_transactions'] = $this->transactions->total(array(
                    'createdby' => $current_user_id,
                ));
            }

            // Customer data - only from current BUMDes
            $data['customers'] = $this->customers->total(array(
                'createdby' => $current_user_id,
            ));

            // Total modal from beginning balance for selected year
            $data['total_modal'] = $this->beginningbalances->sum('beginning_balances', array(
                'YEAR(period)' => $selectedYear,
                'createdby' => $current_user_id,
            )) ?? 0;

            // Get village name for BUMDes special access
            $current_user = getCurrentUser();
            if ($current_user && $current_user->villageid) {
                $village = $this->db->get_where('msvillages', array('code' => $current_user->villageid))->row();
                $data['village_name'] = $village ? $village->name : 'N/A';
            } else {
                $data['village_name'] = 'N/A';
            }

            // Data laporan BUMDes untuk dashboard - temporarily disabled
            // $data['report_stats'] = $this->bumdes_reports->getReportStatistics($current_user_id);
            $data['total_sales'] = $this->transactions->sum('amount', [
                'transactiontype' => 'Pendapatan',
                'status' => 'Lunas',
                'createdby' => getCurrentIdUser(),
            ]) ?? 0;
            $data['total_expenses'] = $this->transactions->sum('amount', [
                'transactiontype' => 'Pengeluaran',
                'status' => 'Lunas',
                'createdby' => getCurrentIdUser(),
            ]) ?? 0;

            $months = array();
            for ($i = 1; $i <= 12; $i++) {
                $month_name = date('F', mktime(0, 0, 0, $i, 10));

                $months[$month_name]['revenue'] = $this->transactions->sum('amount', [
                    'transactiontype' => 'Pendapatan',
                    'status' => 'Lunas',
                    'MONTH(transactiondate)' => $i,
                    'YEAR(transactiondate)' => $selectedYear,
                    'createdby' => getCurrentIdUser(),
                ]) ?? 0;
                $months[$month_name]['expense'] = $this->transactions->sum('amount', [
                    'transactiontype' => 'Pengeluaran',
                    'status' => 'Lunas',
                    'MONTH(transactiondate)' => $i,
                    'YEAR(transactiondate)' => $selectedYear,
                    'createdby' => getCurrentIdUser(),
                ]) ?? 0;
            }

            $data['months'] = json_encode(array_keys($months));
            $data['chart_revenue'] = json_encode(array_column($months, 'revenue'));
            $data['chart_expense'] = json_encode(array_column($months, 'expense'));
            // Broadcast WhatsApp feature temporarily disabled
            // $data['broadcast'] = $this->tb_broadcast->select('a.*, b.total_all, COALESCE(c.total_processed, 0) as total_processed, COALESCE(d.total_success, 0) as total_success')
            //     ->join("(select broadcastid, count(*) as total_all from broadcastdetails group by broadcastid) b", 'a.id = b.broadcastid')
            //     ->join("(select broadcastid, count(*) as total_processed from broadcastdetails where status != 'Menunggu' group by broadcastid) c", 'a.id = c.broadcastid', 'LEFT')
            //     ->join("(select broadcastid, count(*) as total_success from broadcastdetails where status = 'Berhasil' group by broadcastid) d", 'a.id = d.broadcastid', 'LEFT')
            //     ->limit(5)
            //     ->order_by('createddate', 'DESC')
            //     ->result(array(
            //         'a.createdby' => getCurrentIdUser(),
            //     ));
            $data['broadcast'] = array(); // Empty array as placeholder
        }

        return $this->load->view('master', $data);
    }

    public function default()
    {
        return redirect(base_url('dashboard'));
    }
}
