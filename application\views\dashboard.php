<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <div>
        <h6 class="fw-semibold mb-0">Dashboard</h6>
        <?php if (isBumdes() && isset($village_name)): ?>
            <p class="mb-0 text-secondary-light">BUMDes: <strong><?= $village_name ?></strong></p>
        <?php endif; ?>
    </div>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
    </ul>
</div>

<div class="row gy-4">
    <div class="col-xxl-12">
        <div class="row gy-4">
            <div class="col-md-12">
                <div class="card radius-8 border-0">
                    <div class="row">
                        <div class="col-xxl-6 pe-xxl-0">
                            <div class="card-body p-24">
                                <h6 class="mb-2 fw-bold text-lg">Laporan Pendapatan</h6>

                                <ul class="d-flex flex-wrap align-items-center mt-3 gap-3">
                                    <li class="d-flex align-items-center gap-2">
                                        <span class="w-12-px h-12-px radius-2 bg-primary-600"></span>
                                        <span class="text-secondary-light text-sm fw-semibold">Pendapatan:
                                            <span class="text-primary-light fw-bold">Rp <?= IDR($total_income ?? 0) ?></span>
                                        </span>
                                    </li>

                                    <li class="d-flex align-items-center gap-2">
                                        <span class="w-12-px h-12-px radius-2 bg-yellow"></span>
                                        <span class="text-secondary-light text-sm fw-semibold">Pengeluaran:
                                            <span class="text-primary-light fw-bold">Rp <?= IDR($total_expense ?? 0) ?></span>
                                        </span>
                                    </li>
                                </ul>

                                <div class="mt-40">
                                    <div id="paymentStatusChart" class="margin-16-minus"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xxl-6">
                            <div class="row h-100 g-0">
                                <div class="col-6 p-0 m-0">
                                    <div class="card-body p-24 h-100 d-flex flex-column justify-content-center border border-top-0">
                                        <div class="d-flex flex-wrap align-items-center justify-content-between gap-1 mb-8">
                                            <div>
                                                <span class="mb-12 w-44-px h-44-px text-primary-600 bg-primary-light border border-primary-light-white flex-shrink-0 d-flex justify-content-center align-items-center radius-8 h6 mb-12">
                                                    <iconify-icon icon="carbon:money" class="icon"></iconify-icon>
                                                </span>
                                                <span class="mb-1 fw-medium text-secondary-light text-md">Total Pengeluaran</span>
                                                <h6 class="fw-semibold text-primary-light mb-1">Rp <?= IDR($total_expenses) ?></h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6 p-0 m-0">
                                    <div class="card-body p-24 h-100 d-flex flex-column justify-content-center border border-top-0 border-start-0 border-end-0">
                                        <div class="d-flex flex-wrap align-items-center justify-content-between gap-1 mb-8">
                                            <div>
                                                <span class="mb-12 w-44-px h-44-px text-yellow bg-yellow-light border border-yellow-light-white flex-shrink-0 d-flex justify-content-center align-items-center radius-8 h6 mb-12">
                                                    <iconify-icon icon="flowbite:users-group-solid" class="icon"></iconify-icon>
                                                </span>
                                                <span class="mb-1 fw-medium text-secondary-light text-md">Total Pelanggan</span>
                                                <h6 class="fw-semibold text-primary-light mb-1"><?= IDR($customers) ?></h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6 p-0 m-0">
                                    <div class="card-body p-24 h-100 d-flex flex-column justify-content-center border border-top-0 border-bottom-0">
                                        <div class="d-flex flex-wrap align-items-center justify-content-between gap-1 mb-8">
                                            <div>
                                                <span class="mb-12 w-44-px h-44-px text-lilac bg-lilac-light border border-lilac-light-white flex-shrink-0 d-flex justify-content-center align-items-center radius-8 h6 mb-12">
                                                    <iconify-icon icon="majesticons:shopping-cart" class="icon"></iconify-icon>
                                                </span>
                                                <span class="mb-1 fw-medium text-secondary-light text-md">Total Transaksi</span>
                                                <h6 class="fw-semibold text-primary-light mb-1"><?= IDR($total_transactions) ?></h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6 p-0 m-0">
                                    <div class="card-body p-24 h-100 d-flex flex-column justify-content-center border border-top-0 border-start-0 border-end-0 border-bottom-0">
                                        <div class="d-flex flex-wrap align-items-center justify-content-between gap-1 mb-8">
                                            <div>
                                                <span class="mb-12 w-44-px h-44-px text-pink bg-pink-light border border-pink-light-white flex-shrink-0 d-flex justify-content-center align-items-center radius-8 h6 mb-12">
                                                    <iconify-icon icon="ri:discount-percent-fill" class="icon"></iconify-icon>
                                                </span>
                                                <span class="mb-1 fw-medium text-secondary-light text-md">Total Penjualan</span>
                                                <h6 class="fw-semibold text-primary-light mb-1">Rp <?= IDR($total_sales) ?></h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Modal Card removed from BUMDes dashboard -->

            <!-- Laporan BUMDes Section - Temporarily disabled -->
            <?php /*
            <div class="col-md-12">
                <div class="card h-100 radius-8 border">
                    <div class="card-body p-24">
                        <div class="d-flex align-items-center flex-wrap gap-2 justify-content-between mb-20">
                            <h6 class="mb-0 fw-semibold text-lg">Ringkasan Laporan BUMDes</h6>

                            <a href="<?= base_url('bumdes_report') ?>" class="text-primary-600 hover-text-primary d-flex align-items-center gap-1">
                                Lihat Semua
                                <iconify-icon icon="solar:alt-arrow-right-linear" class="icon"></iconify-icon>
                            </a>
                        </div>
            <?php /*
            <div class="row gy-4">
                <div class="col-xxl-3 col-sm-6">
                    <div class="card shadow-none border bg-gradient-start-1 h-100 radius-12">
                        <div class="card-body p-20">
                            <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                <div>
                                    <p class="fw-medium text-primary-light mb-1">Total Laporan</p>
                                    <h6 class="mb-0"><?= isset($report_stats['total_reports']) ? $report_stats['total_reports'] : 0 ?></h6>
                                </div>
                                <div class="w-50-px h-50-px bg-cyan rounded-circle d-flex justify-content-center align-items-center">
                                    <iconify-icon icon="carbon:report" class="text-white text-2xl mb-0"></iconify-icon>
                                </div>
                            </div>
                            <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                                <span class="d-inline-flex align-items-center gap-1 text-success-main">
                                    <iconify-icon icon="bxs:up-arrow" class="text-xs"></iconify-icon> Aktif
                                </span>
                                Laporan BUMDes
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-3 col-sm-6">
                    <div class="card shadow-none border bg-gradient-start-2 h-100 radius-12">
                        <div class="card-body p-20">
                            <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                <div>
                                    <p class="fw-medium text-primary-light mb-1">Total Modal</p>
                                    <h6 class="mb-0">Rp <?= isset($report_stats['total_modal']) ? number_format($report_stats['total_modal'], 0, ',', '.') : '0' ?></h6>
                                </div>
                                <div class="w-50-px h-50-px bg-purple rounded-circle d-flex justify-content-center align-items-center">
                                    <iconify-icon icon="carbon:coins" class="text-white text-2xl mb-0"></iconify-icon>
                                </div>
                            </div>
                            <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                                <span class="d-inline-flex align-items-center gap-1 text-success-main">
                                    <iconify-icon icon="bxs:up-arrow" class="text-xs"></iconify-icon> Modal
                                </span>
                                Investasi BUMDes
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-3 col-sm-6">
                    <div class="card shadow-none border bg-gradient-start-3 h-100 radius-12">
                        <div class="card-body p-20">
                            <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                <div>
                                    <p class="fw-medium text-primary-light mb-1">Total Omset</p>
                                    <h6 class="mb-0">Rp <?= isset($report_stats['total_omset']) ? number_format($report_stats['total_omset'], 0, ',', '.') : '0' ?></h6>
                                </div>
                                <div class="w-50-px h-50-px bg-info rounded-circle d-flex justify-content-center align-items-center">
                                    <iconify-icon icon="carbon:chart-line" class="text-white text-2xl mb-0"></iconify-icon>
                                </div>
                            </div>
                            <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                                <span class="d-inline-flex align-items-center gap-1 text-success-main">
                                    <iconify-icon icon="bxs:up-arrow" class="text-xs"></iconify-icon> Pendapatan
                                </span>
                                Total Penjualan
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-3 col-sm-6">
                    <div class="card shadow-none border bg-gradient-start-4 h-100 radius-12">
                        <div class="card-body p-20">
                            <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                <div>
                                    <p class="fw-medium text-primary-light mb-1">Total Laba</p>
                                    <h6 class="mb-0">Rp <?= isset($report_stats['total_laba']) ? number_format($report_stats['total_laba'], 0, ',', '.') : '0' ?></h6>
                                </div>
                                <div class="w-50-px h-50-px bg-success-main rounded-circle d-flex justify-content-center align-items-center">
                                    <iconify-icon icon="carbon:trophy" class="text-white text-2xl mb-0"></iconify-icon>
                                </div>
                            </div>
                            <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                                <span class="d-inline-flex align-items-center gap-1 text-success-main">
                                    <iconify-icon icon="bxs:up-arrow" class="text-xs"></iconify-icon> Keuntungan
                                </span>
                                Laba Bersih
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            */ ?>
        </div>
    </div>
</div>

<div class="col-md-12">
    <div class="card h-100">
        <div class="card-body p-24">
            <div class="d-flex align-items-center flex-wrap gap-2 justify-content-between mb-20">
                <h6 class="mb-2 fw-bold text-lg mb-0">Transaksi Terbaru</h6>

                <a href="<?= base_url('master/transaction') ?>" class="text-primary-600 hover-text-primary d-flex align-items-center gap-1">
                    Lihat Semua
                    <iconify-icon icon="solar:alt-arrow-right-linear" class="icon"></iconify-icon>
                </a>
            </div>

            <div class="table-responsive scroll-sm">
                <table class="table bordered-table mb-0">
                    <thead>
                        <tr>
                            <th>Kode Transaksi</th>
                            <th>Tanggal Transaksi</th>
                            <th>Catatan Transaksi</th>
                            <th>Unit Kerja</th>
                            <th>Status</th>
                            <th>Nominal</th>
                        </tr>
                    </thead>

                    <tbody>
                        <?php foreach ($transactions as $key => $value): ?>
                            <tr>
                                <td><?= $value->transactioncode ?></td>
                                <td><?= tgl_indo($value->transactiondate) ?></td>
                                <td><?= $value->transactionnote ?></td>
                                <td>
                                    <span class="text-sm text-secondary-light"><?= isset($value->workunitdata) ? $value->workunitdata : 'N/A' ?></span>
                                </td>
                                <td>
                                    <?php if ($value->status == 'Lunas'): ?>
                                        <span class="bg-success-focus text-success-main px-24 py-4 rounded-pill fw-medium text-sm">Lunas</span>
                                    <?php elseif ($value->status == 'Menunggu Pembayaran'): ?>
                                        <span class="bg-warning-focus text-warning-main px-24 py-4 rounded-pill fw-medium text-sm">Menunggu Pembayaran</span>
                                    <?php elseif ($value->status == 'Dibatalkan'): ?>
                                        <span class="bg-danger-focus text-danger-main px-24 py-4 rounded-pill fw-medium text-sm">Dibatalkan</span>
                                    <?php endif; ?>
                                </td>
                                <td>Rp <?= number_format($value->amount, 0, ',', '.') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</div>
</div>

<!-- Broadcast WhatsApp section temporarily disabled -->
<?php if (false): // Temporarily disabled 
?>
    <div class="col-xxl-3 col-md-6">
        <div class="row gy-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex align-items-center flex-wrap gap-2 justify-content-between">
                            <h6 class="mb-2 fw-bold text-lg mb-0">Proses Broadcast</h6>

                            <a href="<?= base_url('broadcast') ?>" class="text-primary-600 hover-text-primary d-flex align-items-center gap-1">
                                Lihat Semua
                                <iconify-icon icon="solar:alt-arrow-right-linear" class="icon"></iconify-icon>
                            </a>
                        </div>
                    </div>

                    <div class="card-body">
                        <?php foreach ($broadcast as $key => $value): ?>
                            <?php
                            $total_all = $value->total_all;
                            $total_processed = $value->total_processed;
                            $total_success = $value->total_success;

                            $progress = 0;
                            if ($total_all > 0) {
                                $progress = ($total_processed / $total_all) * 100;
                            }
                            ?>
                            <div class="d-flex align-items-center justify-content-between gap-3 <?= $key + 1 != count($broadcast) ? 'mb-24' : null ?>">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <h6 class="text-md mb-0 fw-medium">#<?= $value->broadcastcode ?></h6>
                                        <span class="text-sm text-secondary-light fw-medium"><?= tgl_indo(date('Y-m-d', strtotime($value->createddate))) ?></span>
                                    </div>
                                </div>

                                <div>
                                    <div class="progressBar w-90-px h-44-px position-relative text-primary-light fw-semibold">
                                        <div class="barOverflow">
                                            <div class="circleBar border-width-6-px"></div>
                                        </div>

                                        <div class="position-absolute start-50 translate-middle top-50 line-height-1 mt-8">
                                            <div class="d-flex align-items-center justify-content-center line-height-1 text-sm">
                                                <span class="barNumber line-height-1"><?= $progress ?></span>
                                                <span>%</span>
                                            </div>

                                            <span class="line-height-1 text-xs text-secondary-light">Selesai</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>


        </div>
    </div>
<?php endif; // End of temporarily disabled broadcast section 
?>
</div>

<script>
    // Flag to prevent multiple chart initialization
    var dashboardChartInitialized = false;

    // Function to initialize dashboard charts
    function initializeDashboardChart() {
        // Prevent multiple initialization
        if (dashboardChartInitialized) {
            console.log('Dashboard chart already initialized, skipping...');
            return;
        }
        var options = {
            series: [{
                name: 'Pendapatan',
                data: <?= $chart_revenue ?>
            }, {
                name: 'Pengeluaran',
                data: <?= $chart_expense ?>
            }],
            colors: ['#487FFF', '#FF9F29'],
            legend: {
                show: false
            },
            chart: {
                type: 'bar',
                height: 250,
                toolbar: {
                    show: false
                },
            },
            grid: {
                show: true,
                borderColor: '#D1D5DB',
                strokeDashArray: 4, // Use a number for dashed style
                position: 'back',
            },
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    columnWidth: 10,
                },
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: <?= $months ?>,
            },
            yaxis: {
                categories: ['0', '5000', '10,000', '20,000', '30,000', '50,000', '60,000', '60,000', '70,000', '80,000', '90,000', '100,000'],
            },
            fill: {
                opacity: 1,
                width: 18,
            },
        };

        // Check if chart element exists before creating chart
        var chartElement = document.querySelector("#paymentStatusChart");
        if (chartElement) {
            // Check if ApexCharts is available
            if (typeof ApexCharts !== 'undefined') {
                var chart = new ApexCharts(chartElement, options);
                chart.render();
                dashboardChartInitialized = true; // Mark as initialized
                console.log('Dashboard chart initialized successfully');
            } else {
                // Queue for later execution if ApexCharts not loaded yet
                console.log('ApexCharts not loaded yet, queuing dashboard chart');
                window.ApexChartsQueue = window.ApexChartsQueue || [];
                window.ApexChartsQueue.push({
                    element: chartElement,
                    options: options,
                    timestamp: Date.now()
                });
                dashboardChartInitialized = true; // Mark as initialized to prevent multiple queuing
            }
        } else {
            console.warn('Chart element #paymentStatusChart not found');
        }
    }

    // Function to initialize progress bars
    function initializeProgressBars() {
        $(".progressBar").each(function() {
            var $bar = $(this).find(".circleBar");
            var $val = $(this).find(".barNumber");
            var perc = parseInt($val.text(), 10);

            $({
                p: 0
            }).animate({
                p: perc
            }, {
                duration: 3000,
                easing: "swing",
                step: function(p) {
                    $bar.css({
                        transform: "rotate(" + (45 + (p * 1.8)) + "deg)", // 100%=180° so: ° = % * 1.8
                        // 45 is to add the needed rotation to have the green borders at the bottom
                    });
                    $val.text(p | 0);
                }
            });
        });
    }

    // Wait for DOM and scripts to be ready
    window.onload = function() {
        // Wait a bit to ensure all scripts are loaded
        setTimeout(function() {
            initializeDashboardChart();
            initializeProgressBars();
        }, 200); // Increased wait time for better reliability
    };

    // Fallback: Also try when document is ready (with jQuery check)
    function initWithJQuery() {
        if (typeof $ !== 'undefined') {
            $(document).ready(function() {
                setTimeout(function() {
                    // Only initialize if not already done
                    if (!dashboardChartInitialized) {
                        initializeDashboardChart();
                    }
                    initializeProgressBars();
                }, 300);
            });
        } else {
            // jQuery not available yet, try again later
            setTimeout(initWithJQuery, 200);
        }
    }

    // Start jQuery initialization
    setTimeout(initWithJQuery, 100);

    // Additional fallback for ApexCharts loading
    document.addEventListener('DOMContentLoaded', function() {
        // Check periodically if ApexCharts is loaded
        var checkApexCharts = setInterval(function() {
            if (typeof ApexCharts !== 'undefined') {
                clearInterval(checkApexCharts);
                setTimeout(function() {
                    // Only initialize if not already done
                    if (!dashboardChartInitialized) {
                        initializeDashboardChart();
                    }
                }, 100);
            }
        }, 100);

        // Stop checking after 10 seconds
        setTimeout(function() {
            clearInterval(checkApexCharts);
        }, 10000);
    });
</script>