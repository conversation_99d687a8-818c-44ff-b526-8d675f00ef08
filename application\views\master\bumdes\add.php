<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Tambah BUMDes</h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">BUMDes / Tambah</li>
    </ul>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Tambah Transaksi</h5>
    </div>

    <div class="card-body">
        <form id="frm" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off" success-redirect="<?= base_url('master/bumdes') ?>">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label">Nama Pemilik Usaha <span class="text-danger">*</span></label>
                        <input type="text" name="ownername" class="form-control" placeholder="Masukkan Nama Pemilik Usaha" required>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label">Nama Usaha <span class="text-danger">*</span></label>
                        <input type="text" name="businessname" class="form-control" placeholder="Masukkan Nama Usaha" required>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label">Unit Kerja <span class="text-danger">*</span></label>
                        <select name="workunit[]" id="workunit" class="form-control select2" multiple="multiple" required>
                            <?php foreach ($workunits as $workunit): ?>
                                <option value="<?= $workunit->id ?>"><?= $workunit->workunitcode . ' - ' . $workunit->workunitname ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="" class="form-label">Alamat <span class="text-danger">*</span></label>
                        <textarea name="address" class="form-control" placeholder="Masukkan Alamat" required></textarea>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label">Username <span class="text-danger">*</span></label>
                        <input type="text" name="username" class="form-control" placeholder="Masukkan Username" required>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label">Password <span class="text-danger">*</span></label>
                        <input type="password" name="password" class="form-control" placeholder="Masukkan Password" required>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Provinsi <span class="text-danger">*</span></label>
                        <select name="province" id="provinces" class="form-control" required>
                            <?php foreach ($provinces as $province): ?>
                                <option value="<?= $province->code ?>"><?= $province->name ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Kabupaten/Kota <span class="text-danger">*</span></label>
                        <select name="cities" id="cities" class="form-control" required>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Kecamatan <span class="text-danger">*</span></label>
                        <select name="districts" id="districts" class="form-control" required>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Desa <span class="text-danger">*</span></label>
                        <select name="villages" id="villages" class="form-control" required>
                        </select>
                    </div>
                </div>
            </div>

            <div class="text-end mt-3">
                <a href="<?= base_url('master/bumdes') ?>" class="btn btn-danger btn-sm">Kembali</a>
                <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
            </div>
        </form>
    </div>
</div>

<style>
    .select2-container--default .select2-selection--multiple {
        border: 1px solid #e4e6fc;
        border-radius: 8px;
        min-height: 42px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        background-color: #487fff;
        border: 1px solid #487fff;
        color: white;
        border-radius: 4px;
        padding: 2px 8px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        color: white;
        margin-right: 5px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
        color: #ff6b6b;
    }
</style>

<script>
    window.onload = function() {
        // Initialize Select2 for workunit
        $('#workunit').select2({
            placeholder: "Pilih Unit Usaha",
            allowClear: false,
            width: '100%',
            theme: 'default'
        });

        $('#provinces').change(function() {
            let code = $(this).val();

            $.ajax({
                url: '<?= base_url('master/select/cities') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    code: code
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('#cities').html(response.CONTENT).trigger('change');
                    }
                }
            });
        });

        $('#cities').change(function() {
            let code = $(this).val();

            $.ajax({
                url: '<?= base_url('master/select/districts') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    code: code
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('#districts').html(response.CONTENT).trigger('change');
                    }
                }
            });
        });

        $('#districts').change(function() {
            let code = $(this).val();

            $.ajax({
                url: '<?= base_url('master/select/villages') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    code: code
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('#villages').html(response.CONTENT).trigger('change');
                    }
                }
            });
        });

        $('#provinces').trigger('change');
    };
</script>